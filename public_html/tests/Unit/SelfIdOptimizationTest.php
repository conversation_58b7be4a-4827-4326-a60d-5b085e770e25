<?php

namespace Tests\Unit;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\DataUser;
use App\Models\DataUserSelfId;
use App\Models\LiveMe;
use App\Models\LiveMeSelfId;
use App\Models\User;
use Carbon\Carbon;

class SelfIdOptimizationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->testUser = User::create([
            'name' => 'Test User',
            'username' => 'testuser',
            'password' => bcrypt('password'),
            'user_token' => 'TEST123',
            'min_second' => 60,
            'max_second' => 3600,
            'coins' => 1.0
        ]);
    }

    /** @test */
    public function it_can_check_if_self_id_is_distributed_to_data_user()
    {
        $dataUser = DataUser::create([
            'user_token' => 'TEST123',
            'user_name' => 'Test User',
            'countdown' => Carbon::now()->addMinutes(30)->timestamp,
            'limit' => 5,
            'act_id' => 'test_act_123'
        ]);

        $selfId = 'test_self_id_123';

        // Initially should not be distributed
        $this->assertFalse($dataUser->hasSelfId($selfId));

        // Add self_id
        $dataUser->addSelfId($selfId, '127.0.0.1', 'Test User Agent');

        // Now should be distributed
        $this->assertTrue($dataUser->hasSelfId($selfId));
    }

    /** @test */
    public function it_can_get_distributed_self_ids_for_data_user()
    {
        $dataUser = DataUser::create([
            'user_token' => 'TEST123',
            'user_name' => 'Test User',
            'act_id' => 'test_act_456'
        ]);

        $selfIds = ['self_id_1', 'self_id_2', 'self_id_3'];

        foreach ($selfIds as $selfId) {
            $dataUser->addSelfId($selfId);
        }

        $distributedSelfIds = $dataUser->getDistributedSelfIds();

        $this->assertCount(3, $distributedSelfIds);
        $this->assertEquals($selfIds, $distributedSelfIds);
    }

    /** @test */
    public function it_can_filter_data_users_not_distributed_to_self_id()
    {
        $dataUser1 = DataUser::create(['user_token' => 'TEST123', 'act_id' => 'act1']);
        $dataUser2 = DataUser::create(['user_token' => 'TEST123', 'act_id' => 'act2']);
        $dataUser3 = DataUser::create(['user_token' => 'TEST123', 'act_id' => 'act3']);

        $selfId = 'test_self_id';

        // Distribute to dataUser1 only
        $dataUser1->addSelfId($selfId);

        // Query for data users not distributed to this self_id
        $notDistributed = DataUser::where('user_token', 'TEST123')
            ->notDistributedToSelfId($selfId)
            ->get();

        $this->assertCount(2, $notDistributed);
        $this->assertTrue($notDistributed->contains($dataUser2));
        $this->assertTrue($notDistributed->contains($dataUser3));
        $this->assertFalse($notDistributed->contains($dataUser1));
    }

    /** @test */
    public function it_can_check_if_self_id_is_distributed_to_live_me()
    {
        $liveMe = LiveMe::create([
            'user_token' => 'TEST123',
            'redpkt_id' => 'test_redpkt_123',
            'grab_end_time' => Carbon::now()->addMinutes(30)->timestamp,
            'limit' => 5
        ]);

        $selfId = 'test_self_id_123';

        // Initially should not be distributed
        $this->assertFalse($liveMe->hasSelfId($selfId));

        // Add self_id
        $liveMe->addSelfId($selfId, '127.0.0.1', 'Test User Agent');

        // Now should be distributed
        $this->assertTrue($liveMe->hasSelfId($selfId));
    }

    /** @test */
    public function it_prevents_duplicate_self_id_distribution()
    {
        $dataUser = DataUser::create(['user_token' => 'TEST123', 'act_id' => 'test_act_dup']);
        $selfId = 'test_self_id';

        // Add self_id first time
        $result1 = $dataUser->addSelfId($selfId);
        $this->assertNotNull($result1);

        // Try to add same self_id again
        $result2 = $dataUser->addSelfId($selfId);
        $this->assertNull($result2);

        // Should only have one record
        $this->assertEquals(1, $dataUser->countDistributedSelfIds());
    }

    /** @test */
    public function it_respects_limit_constraint()
    {
        $dataUser = DataUser::create([
            'user_token' => 'TEST123',
            'act_id' => 'test_act_limit',
            'limit' => 3 // Chỉ cho phép 3 self_id
        ]);

        // Thêm 3 self_id (trong giới hạn)
        for ($i = 1; $i <= 3; $i++) {
            $result = $dataUser->addSelfId("self_id_$i");
            $this->assertNotNull($result);
        }

        // Thử thêm self_id thứ 4 (vượt quá limit)
        $result = $dataUser->addSelfId("self_id_4");
        $this->assertNull($result); // Should return null vì đã vượt limit

        // Kiểm tra số lượng
        $this->assertEquals(3, $dataUser->countDistributedSelfIds());
    }

    /** @test */
    public function query_excludes_data_users_at_limit()
    {
        $dataUser1 = DataUser::create([
            'user_token' => 'TEST123',
            'act_id' => 'test_act_1',
            'limit' => 2
        ]);

        $dataUser2 = DataUser::create([
            'user_token' => 'TEST123',
            'act_id' => 'test_act_2',
            'limit' => 3
        ]);

        // DataUser1 đã đạt limit (2/2)
        $dataUser1->addSelfId('other_self_id_1');
        $dataUser1->addSelfId('other_self_id_2');

        // DataUser2 chưa đạt limit (1/3)
        $dataUser2->addSelfId('other_self_id_3');

        // Query cho self_id mới
        $availableDataUsers = DataUser::where('user_token', 'TEST123')
            ->notDistributedToSelfId('new_self_id')
            ->notAtLimit()
            ->get();

        // Chỉ dataUser2 nên được trả về vì dataUser1 đã đạt limit
        $this->assertCount(1, $availableDataUsers);
        $this->assertEquals($dataUser2->id, $availableDataUsers->first()->id);
    }

    /** @test */
    public function it_handles_dynamic_limit_with_tinh_toan_so_xo()
    {
        $dataUser = DataUser::create([
            'user_token' => 'TEST123',
            'act_id' => 'test_dynamic_limit',
            'award_count' => 1000,
            'win_population' => 100,
            'gift_price' => 5,
            'limit' => 50 // Limit gốc
        ]);

        // Tính toán dynamic limit: award_count/win_population = 1000/100 = 10 > gift_price (5)
        // Nên dynamic limit = win_population = 100

        // Thêm self_id với dynamic limit = 100
        for ($i = 1; $i <= 100; $i++) {
            $result = $dataUser->addSelfId("self_id_$i", null, null, 100);
            $this->assertNotNull($result);
        }

        // Thử thêm self_id thứ 101 (vượt dynamic limit)
        $result = $dataUser->addSelfId("self_id_101", null, null, 100);
        $this->assertNull($result); // Should return null vì đã vượt dynamic limit

        // Kiểm tra số lượng
        $this->assertEquals(100, $dataUser->countDistributedSelfIds());
    }

    /** @test */
    public function tinh_toan_so_xo_returns_correct_values()
    {
        $controller = new \App\Http\Controllers\Api\ApiController();

        // Test case 1: Có lời (award_count/win_population > gift_price)
        $result1 = $controller->tinhToanSoXo(1000, 100, 5); // 1000/100 = 10 > 5
        $this->assertEquals(100, $result1); // Should return win_population

        // Test case 2: Lỗ (award_count/win_population <= gift_price)
        $result2 = $controller->tinhToanSoXo(500, 100, 10); // 500/100 = 5 <= 10
        $this->assertEquals(0, $result2); // Should return 0

        // Test case 3: Hoà vốn
        $result3 = $controller->tinhToanSoXo(1000, 100, 10); // 1000/100 = 10 = 10
        $this->assertEquals(0, $result3); // Should return 0
    }

    /** @test */
    public function junction_table_has_proper_indexes()
    {
        // This test ensures our indexes are working by checking query performance
        $dataUser = DataUser::create(['user_token' => 'TEST123', 'act_id' => 'test_act_perf']);

        // Create many self_id distributions
        for ($i = 1; $i <= 100; $i++) {
            $dataUser->addSelfId("self_id_$i");
        }

        $startTime = microtime(true);

        // This query should be fast due to indexes
        $exists = DataUserSelfId::where('data_user_id', $dataUser->id)
            ->where('self_id', 'self_id_50')
            ->exists();

        $endTime = microtime(true);
        $queryTime = $endTime - $startTime;

        $this->assertTrue($exists);
        $this->assertLessThan(0.1, $queryTime); // Should complete in less than 100ms
    }
}
