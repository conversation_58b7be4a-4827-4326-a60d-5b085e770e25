<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class MigrateSelfIdDataToJunctionTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Migrate data_users self_id data
        $this->migrateDataUsersSelfIds();
        
        // Migrate live_mes self_id data
        $this->migrateLiveMesSelfIds();
    }

    /**
     * Migrate self_id data from data_users table to data_user_self_ids junction table
     */
    private function migrateDataUsersSelfIds()
    {
        $batchSize = 1000;
        $offset = 0;
        
        do {
            $dataUsers = DB::table('data_users')
                ->whereNotNull('self_id')
                ->where('self_id', '!=', '')
                ->where('self_id', '!=', ',')
                ->offset($offset)
                ->limit($batchSize)
                ->get(['id', 'self_id', 'created_at']);
            
            $insertData = [];
            
            foreach ($dataUsers as $dataUser) {
                // Parse self_id string (format: ",selfid1,selfid2,selfid3,")
                $selfIds = array_filter(explode(',', $dataUser->self_id));
                
                foreach ($selfIds as $selfId) {
                    $selfId = trim($selfId);
                    if (!empty($selfId)) {
                        $insertData[] = [
                            'data_user_id' => $dataUser->id,
                            'self_id' => $selfId,
                            'distributed_at' => $dataUser->created_at,
                            'created_at' => Carbon::now(),
                            'updated_at' => Carbon::now()
                        ];
                    }
                }
            }
            
            if (!empty($insertData)) {
                // Insert in chunks to avoid memory issues
                $chunks = array_chunk($insertData, 500);
                foreach ($chunks as $chunk) {
                    DB::table('data_user_self_ids')->insertOrIgnore($chunk);
                }
            }
            
            $offset += $batchSize;
            
        } while ($dataUsers->count() == $batchSize);
    }

    /**
     * Migrate self_id data from live_mes table to live_me_self_ids junction table
     */
    private function migrateLiveMesSelfIds()
    {
        $batchSize = 1000;
        $offset = 0;
        
        do {
            $liveMes = DB::table('live_mes')
                ->whereNotNull('self_id')
                ->where('self_id', '!=', '')
                ->where('self_id', '!=', ',')
                ->offset($offset)
                ->limit($batchSize)
                ->get(['id', 'self_id', 'created_at']);
            
            $insertData = [];
            
            foreach ($liveMes as $liveMe) {
                // Parse self_id string (format: ",selfid1,selfid2,selfid3,")
                $selfIds = array_filter(explode(',', $liveMe->self_id));
                
                foreach ($selfIds as $selfId) {
                    $selfId = trim($selfId);
                    if (!empty($selfId)) {
                        $insertData[] = [
                            'live_me_id' => $liveMe->id,
                            'self_id' => $selfId,
                            'distributed_at' => $liveMe->created_at,
                            'created_at' => Carbon::now(),
                            'updated_at' => Carbon::now()
                        ];
                    }
                }
            }
            
            if (!empty($insertData)) {
                // Insert in chunks to avoid memory issues
                $chunks = array_chunk($insertData, 500);
                foreach ($chunks as $chunk) {
                    DB::table('live_me_self_ids')->insertOrIgnore($chunk);
                }
            }
            
            $offset += $batchSize;
            
        } while ($liveMes->count() == $batchSize);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Clear junction tables
        DB::table('data_user_self_ids')->truncate();
        DB::table('live_me_self_ids')->truncate();
    }
}
