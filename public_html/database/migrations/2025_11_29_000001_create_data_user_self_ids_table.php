<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDataUserSelfIdsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('data_user_self_ids', function (Blueprint $table) {
            $table->id();
            
            // Foreign keys
            $table->unsignedInteger('data_user_id')->comment('ID của data_user từ bảng data_users');
            $table->string('self_id', 100)->comment('Self ID đã được phân phối');
            
            // Metadata
            $table->timestamp('distributed_at')->useCurrent()->comment('Thời gian phân phối');
            $table->string('ip_address', 45)->nullable()->comment('IP address khi phân phối');
            $table->text('user_agent')->nullable()->comment('User agent khi phân phối');
            
            $table->timestamps();
            
            // Foreign key constraints
            $table->foreign('data_user_id')->references('id')->on('data_users')->onDelete('cascade');
            
            // Indexes để tối ưu performance
            $table->index('data_user_id');
            $table->index('self_id');
            $table->index(['data_user_id', 'self_id']); // Composite index cho unique check
            $table->index('distributed_at');
            $table->index(['self_id', 'distributed_at']); // Để tìm self_id theo thời gian
            
            // Unique constraint để đảm bảo một self_id chỉ được phân phối một lần cho một data_user
            $table->unique(['data_user_id', 'self_id'], 'unique_data_user_self_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('data_user_self_ids');
    }
}
