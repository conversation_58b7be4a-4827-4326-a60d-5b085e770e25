<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLiveMeSelfIdsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('live_me_self_ids', function (Blueprint $table) {
            $table->id();
            
            // Foreign keys
            $table->unsignedBigInteger('live_me_id')->comment('ID của live_me từ bảng live_mes');
            $table->string('self_id', 100)->comment('Self ID đã được phân phối');
            
            // Metadata
            $table->timestamp('distributed_at')->useCurrent()->comment('Thời gian phân phối');
            $table->string('ip_address', 45)->nullable()->comment('IP address khi phân phối');
            $table->text('user_agent')->nullable()->comment('User agent khi phân phối');
            
            $table->timestamps();
            
            // Foreign key constraints
            $table->foreign('live_me_id')->references('id')->on('live_mes')->onDelete('cascade');
            
            // Indexes để tối ưu performance
            $table->index('live_me_id');
            $table->index('self_id');
            $table->index(['live_me_id', 'self_id']); // Composite index cho unique check
            $table->index('distributed_at');
            $table->index(['self_id', 'distributed_at']); // Để tìm self_id theo thời gian
            
            // Unique constraint để đảm bảo một self_id chỉ được phân phối một lần cho một live_me
            $table->unique(['live_me_id', 'self_id'], 'unique_live_me_self_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('live_me_self_ids');
    }
}
