<?php

namespace App\Http\Controllers;

use App\Models\LiveRoomExDiamond;
use App\Models\LiveRoomHistory;
use App\Models\User;
use App\Models\SelfUidTracking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Yajra\DataTables\Facades\DataTables;

class LiveRoomExDiamondController extends Controller
{
    /**
     * Display the main page for Sàng Diamond
     */
    public function index($token)
    {
        $user = User::where('user_token', $token)->first();

        if (!$user) {
            abort(404, 'User not found');
        }

        // Only allow non-admin users
        if ($user->role == 1) {
            abort(403, 'Access denied for admin users');
        }

        if (request()->ajax()) {
            $data = LiveRoomExDiamond::where('user_token', $token)
                ->orderBy('created_at', 'desc')
                ->get();

            return DataTables::of($data)
                ->addColumn('action', function($row) {
                    $html = '';
                    // Toggle active/inactive button
                    if ($row->is_active) {
                        $html .= '<button type="button" data-href="'.route('live-room-ex-diamond.toggle', $row->id).'" class="btn btn-warning btn-sm mr-1 toggle-btn"><i class="fa fa-pause"></i> Tạm ngưng</button>';
                    } else {
                        $html .= '<button type="button" data-href="'.route('live-room-ex-diamond.toggle', $row->id).'" class="btn btn-success btn-sm mr-1 toggle-btn"><i class="fa fa-play"></i> Kích hoạt</button>';
                    }
                    return $html;
                })
                ->addColumn('external_link', function($row) {
                    return '<a href="'.$row->external_link.'" target="_blank" class="btn btn-sm btn-info"><i class="fa fa-external-link-alt"></i> External Link</a>';
                })
                ->editColumn('is_active', function($row) {
                    return $row->is_active ? '<span class="badge badge-success">Hoạt động</span>' : '<span class="badge badge-warning">Tạm ngưng</span>';
                })
                ->editColumn('is_used', function($row) {
                    return $row->is_used ? '<span class="badge badge-danger">Chờ kích hoạt "External link"</span>' : '<span class="badge badge-primary">Đợi máy thụ</span>';
                })
                ->editColumn('created_at', function($row) {
                    return $row->created_at->format('d-m-Y H:i:s');
                })
                ->editColumn('room_name', function($row) {
                    return '<strong>' . htmlspecialchars($row->room_name) . '</strong>';
                })
                ->rawColumns(['action', 'external_link', 'is_active', 'is_used', 'room_name'])
                ->make(true);
        }

        return view('live_room_ex_diamond.index', compact('token', 'user'));
    }

    /**
     * Get history data for DataTable
     */
    public function getHistoryData(Request $request, $token)
    {
        // Check if user exists and has permission
        $user = User::where('user_token', $token)->first();
        if (!$user || $user->role == 1) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        if ($request->ajax()) {
            // Get histories for rooms belonging to this user
            $histories = LiveRoomHistory::with(['liveRoomExDiamond'])
                ->where('user_token', $token)
                ->orderBy('created_at', 'desc');

            return DataTables::of($histories)
                ->addColumn('room_info', function($row) {
                    $room = $row->liveRoomExDiamond;
                    if ($room) {
                        return '<strong>' . htmlspecialchars($room->room_name) . '</strong><br>' .
                               '<small class="text-muted">' . $room->room_url . '</small>';
                    }
                    return '<span class="text-danger">Room deleted</span>';
                })
                ->editColumn('self_uid', function($row) {
                    return '<code>' . $row->self_uid . '</code>';
                })
                ->editColumn('diamond_balance', function($row) {
                    return '<span class="badge badge-info">' . number_format($row->diamond_balance) . '</span>';
                })
                ->editColumn('status', function($row) {
                    if ($row->isSuccessful()) {
                        return '<span class="badge badge-success">Thành công</span>';
                    } else {
                        return '<span class="badge badge-warning">Chưa thả Fan</span>';
                    }
                })
                ->editColumn('points_used', function($row) {
                    if ($row->points_used) {
                        return '<span class="badge badge-primary">' . number_format($row->points_used) . '</span>';
                    }
                    return '<span class="text-muted">-</span>';
                })
                ->editColumn('created_at', function($row) {
                    return $row->created_at->format('d-m-Y H:i:s');
                })
                ->editColumn('success_at', function($row) {
                    if ($row->success_at) {
                        return $row->success_at->format('d-m-Y H:i:s');
                    }
                    return '<span class="text-muted">-</span>';
                })
                ->rawColumns(['room_info', 'self_uid', 'diamond_balance', 'status', 'points_used', 'success_at'])
                ->make(true);
        }

        return response()->json(['error' => 'Invalid request'], 400);
    }

    /**
     * Show the form for creating a new room
     */
    public function create($token)
    {
        return view('live_room_ex_diamond.create', compact('token'));
    }

    /**
     * Store a newly created room
     */
    public function store(Request $request, $token)
    {
        $validator = Validator::make($request->all(), [
            'room_name' => 'required|string|max:255',
            'room_url' => 'required|url|regex:/^https:\/\/slink\.bigovideo\.tv\/[a-zA-Z0-9]+$/'
        ], [
            'room_name.required' => 'Tên phòng là bắt buộc',
            'room_name.string' => 'Tên phòng phải là chuỗi ký tự',
            'room_name.max' => 'Tên phòng không được quá 255 ký tự',
            'room_url.required' => 'URL phòng là bắt buộc',
            'room_url.url' => 'URL phòng không hợp lệ',
            'room_url.regex' => 'URL phòng phải có định dạng https://slink.bigovideo.tv/...'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if user exists
        $user = User::where('user_token', $token)->first();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'User not found'
            ], 404);
        }

        // Create new room (is_used = true initially, will be set to false when external link is accessed)
        $room = LiveRoomExDiamond::create([
            'user_token' => $token,
            'room_name' => $request->room_name,
            'room_url' => $request->room_url,
            'token' => LiveRoomExDiamond::generateToken(),
            'external_token' => LiveRoomExDiamond::generateExternalToken(),
            'is_active' => true,
            'is_used' => true,
            'used_count' => 0
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Thêm phòng thành công!',
            'data' => $room
        ]);
    }

    /**
     * Toggle active status of a room
     */
    public function toggleActive($id)
    {
        $room = LiveRoomExDiamond::find($id);

        if (!$room) {
            return response()->json([
                'success' => false,
                'message' => 'Phòng không tồn tại'
            ], 404);
        }

        $room->toggleActive();

        return response()->json([
            'success' => true,
            'message' => $room->is_active ? 'Kích hoạt phòng thành công!' : 'Tạm ngưng phòng thành công!'
        ]);
    }

    /**
     * Activate room when external link is accessed
     */
    public function activateRoom($externalToken)
    {
        // Lấy room theo token trước (không lọc is_active, is_used)
        $room = LiveRoomExDiamond::where('external_token', $externalToken)->first();

        // Không có room khớp token
        if (!$room) {
            return response()->json([
                'success' => false,
                'message' => 'Token không ton tai hoac khong hop le',
                'external_token' => $externalToken
            ], 404, [], JSON_UNESCAPED_UNICODE);
        }

        // Room không kích hoạt
        if (!$room->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Room Link dang tam ngung.',
                'external_token' => $externalToken
            ], 400, [], JSON_UNESCAPED_UNICODE);
        }

        // Room chưa dùng hoặc đã hết lượt
        if (!$room->is_used) {
            return response()->json([
                'success' => false,
                'message' => 'Room dang cho may thu vao tang.',
                'external_token' => $externalToken
            ], 400, [], JSON_UNESCAPED_UNICODE);
        }

        // Trạng thái hợp lệ → kích hoạt lại room
        $room->resetForReuse();

        return response()->json([
            'success' => true,
            'message' => 'Room da duoc kich hoat va cho may thu vao!',
            'room_url' => $room->room_url
        ], 200, [], JSON_UNESCAPED_UNICODE);
    }

    /**
     * Admin view - Show all rooms from all users
     */
    public function adminIndex()
    {
        // Check if user is admin
        $user = Auth::user();
        if (!$user || $user->role != 1) {
            abort(403, 'Access denied. Admin only.');
        }

        return view('live_room_ex_diamond.admin_index');
    }

    /**
     * Admin data - Get all rooms data for DataTable
     */
    public function getAdminData(Request $request)
    {
        // Check if user is admin
        $user = Auth::user();
        if (!$user || $user->role != 1) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        if ($request->ajax()) {
            $rooms = LiveRoomExDiamond::with(['user'])
                ->orderBy('created_at', 'desc');

            return DataTables::of($rooms)
                ->addColumn('user_info', function($row) {
                    $user = $row->user;
                    if ($user) {
                        return '<strong>' . htmlspecialchars($user->name) . '</strong><br>' .
                               '<small class="text-muted">' . $row->user_token . '</small>';
                    }
                    return '<span class="text-danger">User not found</span><br>' .
                           '<small class="text-muted">' . $row->user_token . '</small>';
                })
                ->editColumn('room_name', function($row) {
                    return '<strong>' . htmlspecialchars($row->room_name) . '</strong>';
                })
                ->editColumn('room_url', function($row) {
                    return '<a href="' . $row->room_url . '" target="_blank" class="text-primary">' .
                           htmlspecialchars($row->room_url) . '</a>';
                })
                ->addColumn('external_link', function($row) {
                    $link = route('activate-room', $row->external_token);
                    return '<a href="' . $link . '" target="_blank" class="btn btn-sm btn-info">' .
                           '<i class="fas fa-external-link-alt"></i> External Link</a>';
                })
                ->editColumn('is_active', function($row) {
                    if ($row->is_active) {
                        return '<span class="badge badge-success">Hoạt động</span>';
                    } else {
                        return '<span class="badge badge-secondary">Tạm ngưng</span>';
                    }
                })
                ->editColumn('is_used', function($row) {
                    if ($row->is_used) {
                        return '<span class="badge badge-warning">Đã sử dụng</span>';
                    } else {
                        return '<span class="badge badge-success">Sẵn sàng</span>';
                    }
                })
                ->editColumn('used_count', function($row) {
                    return '<span class="badge badge-info">' . $row->used_count . '</span>';
                })
                ->editColumn('created_at', function($row) {
                    return $row->created_at->format('d-m-Y H:i:s');
                })
                ->rawColumns(['user_info', 'room_name', 'room_url', 'external_link', 'is_active', 'is_used', 'used_count'])
                ->make(true);
        }

        return response()->json(['error' => 'Invalid request'], 400);
    }

    /**
     * Admin history data - Get all history data for DataTable
     */
    public function getAdminHistoryData(Request $request)
    {
        // Check if user is admin
        $user = Auth::user();
        if (!$user || $user->role != 1) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        if ($request->ajax()) {
            $histories = LiveRoomHistory::with(['liveRoomExDiamond', 'user'])
                ->orderBy('created_at', 'desc');

            return DataTables::of($histories)
                ->addColumn('user_info', function($row) {
                    $user = $row->user;
                    if ($user) {
                        return '<strong>' . htmlspecialchars($user->name) . '</strong><br>' .
                               '<small class="text-muted">' . $row->user_token . '</small>';
                    }
                    return '<span class="text-danger">User not found</span><br>' .
                           '<small class="text-muted">' . $row->user_token . '</small>';
                })
                ->addColumn('room_info', function($row) {
                    $room = $row->liveRoomExDiamond;
                    if ($room) {
                        return '<strong>' . htmlspecialchars($room->room_name) . '</strong><br>' .
                               '<small class="text-muted">' . $room->room_url . '</small>';
                    }
                    return '<span class="text-danger">Room deleted</span>';
                })
                ->editColumn('self_uid', function($row) {
                    return '<code>' . $row->self_uid . '</code>';
                })
                ->editColumn('diamond_balance', function($row) {
                    return '<span class="badge badge-info">' . number_format($row->diamond_balance) . '</span>';
                })
                ->editColumn('status', function($row) {
                    if ($row->isSuccessful()) {
                        return '<span class="badge badge-success">Thành công</span>';
                    } else {
                        return '<span class="badge badge-warning">Chờ xác nhận</span>';
                    }
                })
                ->editColumn('points_used', function($row) {
                    if ($row->points_used) {
                        return '<span class="badge badge-primary">' . number_format($row->points_used) . '</span>';
                    }
                    return '<span class="text-muted">-</span>';
                })
                ->editColumn('created_at', function($row) {
                    return $row->created_at->format('d-m-Y H:i:s');
                })
                ->editColumn('success_at', function($row) {
                    if ($row->success_at) {
                        return $row->success_at->format('d-m-Y H:i:s');
                    }
                    return '<span class="text-muted">-</span>';
                })
                ->rawColumns(['user_info', 'room_info', 'self_uid', 'diamond_balance', 'status', 'points_used', 'success_at'])
                ->make(true);
        }

        return response()->json(['error' => 'Invalid request'], 400);
    }
}
