<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\DataUser;
use App\Models\DataUserSelfId;
use App\Models\LiveMe;
use App\Models\LiveMeSelfId;
use App\Models\SelfUidTracking;
use App\Models\SelfUid;
use App\Models\User;
use App\Models\LiveRoomExDiamond;
use App\Models\LiveRoomHistory;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ApiController extends Controller
{

    public function importData(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'data' => 'required',
            'user_token' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), Response::HTTP_BAD_REQUEST);
        }
        $data_user = DataUser::where('user_token', $request->user_token)->pluck('act_id')->toArray();
        $user_token = User::where('user_token', $request->user_token)->first();
        if (!isset($user_token)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Unknown user token'
            ],500);
        }else{
            $data = $request->data;
            $arr_data = [];
            $value = [];
            $value1 = [];
            $error = 0;
            foreach ($data as $da) {
                if (isset($da['act_id'])) {
                    if (!in_array($da['act_id'], $arr_data) && !in_array($da['act_id'], $data_user)) {
                        $arr_data[] = $da['act_id'];
                        $value[] = [
                            'user_token' => $request->user_token,
                            'user_name' => isset($da['user_name']) ? $da['user_name'] : '',
                            'host_id' => isset($da['host_id']) ? $da['host_id'] : 0,
                            'room_id' => isset($da['room_id']) ? $da['room_id'] : 0,
                            'condition_type' => isset($da['condition_type']) ? $da['condition_type'] : 0,
                            'prize_type' => isset($da['prize_type']) ? $da['prize_type'] : 0,
                            'award_count' => isset($da['award_count']) ? $da['award_count'] : 0,
                            'win_population' => isset($da['win_population']) ? $da['win_population'] : 0,
                            'gift_id' => isset($da['gift_id']) ? $da['gift_id'] : 0,
                            'gift_price' => isset($da['gift_price']) ? $da['gift_price'] : 0,
                            'countdown' => isset($da['countdown']) ? $da['countdown'] + Carbon::now()->timestamp : 0,
                            'act_id' => isset($da['act_id']) ? $da['act_id'] : '',
                            'join_type' => isset($da['join_type']) ? $da['join_type'] : 2,
                            'limit' => $user_token->limit,
                            'self_id' => ',',
                            'created_at' => Carbon::now(),
                        ];

                        if (!empty($user_token->share_room)) {
                            foreach (explode(',', $user_token->share_room) as $share) {
                                if (!in_array($da['act_id'], DataUser::where('user_token', $share)->pluck('act_id')->toArray())) {
                                    $value1[] = [
                                        'user_token' => $share,
                                        'user_name' => isset($da['user_name']) ? $da['user_name'] : '',
                                        'host_id' => isset($da['host_id']) ? $da['host_id'] : 0,
                                        'room_id' => isset($da['room_id']) ? $da['room_id'] : 0,
                                        'condition_type' => isset($da['condition_type']) ? $da['condition_type'] : 0,
                                        'prize_type' => isset($da['prize_type']) ? $da['prize_type'] : 0,
                                        'award_count' => isset($da['award_count']) ? $da['award_count'] : 0,
                                        'win_population' => isset($da['win_population']) ? $da['win_population'] : 0,
                                        'gift_id' => isset($da['gift_id']) ? $da['gift_id'] : 0,
                                        'gift_price' => isset($da['gift_price']) ? $da['gift_price'] : 0,
                                        'countdown' => isset($da['countdown']) ? $da['countdown'] + Carbon::now()->timestamp : 0,
                                        'act_id' => isset($da['act_id']) ? $da['act_id'] : '',
                                        'join_type' => isset($da['join_type']) ? $da['join_type'] : 2,
                                        'limit' => $user_token->limit,
                                        'self_id' => ',',
                                        'created_at' => Carbon::now(),
                                    ];
                                }
                            }
                        }
                    }else{
                        $error++;
                    }
                }
            }

            DataUser::insert($value);

            DataUser::insert($value1);

            if ($error == 0) {
                return response()->json([
                    'status' => 'success'
                ]);
            }else{
                return response()->json([
                    'status' => 'error',
                    'msg' => 'There are '.$error. ' have duplicate data'
                ]);
            }

        }
    }

    public function getData($token, $selfid)
    {
        // Lưu thông tin tracking trước khi xử lý
        $parsedData = $this->saveSelfUidTracking($token, $selfid, request());

        // 5CHHN cho văng tweak
        if($token == '5CHHN'){
            return '{
                "status":"success",
            }';
        }
        $parts = explode('_', $selfid);

        // Trường hợp chỉ có selfUid cho văng tweak
        if (count($parts) == 1) {
            return '{
                "status":"success",
            }';
        }

        $user = User::where('user_token', $token)->first();

        // Kiểm tra nếu user không tồn tại
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'User not found',
                'data' => []
            ], 404);
        }

        // Lấy tất cả candidates không phân biệt gift_id
        $candidates = DataUser::where('user_token', $token)
                        ->notDistributedToSelfId($selfid)
                        ->whereRaw("countdown - UNIX_TIMESTAMP(now()) > ?", $user->min_second)
                        ->whereRaw("countdown - UNIX_TIMESTAMP(now()) < ?", $user->max_second)
                        ->when($user->join_type != 0, function($query) use ($user){
                            $query->where('join_type', $user->join_type);
                        })
                        ->when(!empty($user->condition_type), function($query) use ($user){
                            $query->whereIn('condition_type', explode(',', $user->condition_type));
                        })
                        ->when(!empty($user->prize_type), function($query) use ($user){
                            $query->whereIn('prize_type', explode(',', $user->prize_type));
                        })
                        ->get();

        $data = null;
        $dynamicLimit = null;

        foreach ($candidates as $candidate) {
            // Kiểm tra điều kiện cơ bản
            if ($candidate->award_count / $candidate->win_population < $user->coins) {
                continue;
            }

            // Nếu diamond_balance < 5, áp dụng logic tinhToanSoXo
            if (isset($parsedData['diamond_balance']) && $parsedData['diamond_balance'] < 5) {
                $soXoTinhToan = $this->tinhToanSoXo($candidate->award_count, $candidate->win_population, $candidate->gift_price ?? 0);

                if ($soXoTinhToan == 0) {
                    continue; // Bỏ qua record này
                }

                // Kiểm tra nếu số lượng đã phân phối < soXoTinhToan
                $currentDistributed = $candidate->countDistributedSelfIds();
                if ($currentDistributed >= $soXoTinhToan) {
                    continue; // Đã đạt limit tính toán
                }

                $dynamicLimit = $soXoTinhToan;
            } else {
                // Logic cũ: kiểm tra limit thông thường
                $currentDistributed = $candidate->countDistributedSelfIds();
                if ($candidate->limit && $currentDistributed >= $candidate->limit) {
                    continue;
                }
            }

            // Tìm thấy candidate phù hợp
            $data = $candidate;
            break;
        }

        // $ip = self::checkIp();
        // Log::channel('log_ip')->info($ip.' - '.$user->user_token.' - '.$user->name);
        if(isset($data)){
            // Thêm self_id vào bảng junction với dynamic limit nếu có
            $result = $data->addSelfId(
                $parsedData['self_uid'],
                $this->checkIp(),
                request()->header('User-Agent'),
                $dynamicLimit
            );

            if (!$result) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Failed to add self_id or limit exceeded',
                    'data' => []
                ], 400);
            }

            if ($data->condition_type == 1) {
                $condition_type = 'Send gift';
            }elseif($data->condition_type == 2){
                $condition_type = 'Share the room';
            }elseif($data->condition_type == 3){
                $condition_type = 'Say the password';
            }elseif($data->condition_type == 4){
                $condition_type = 'Join the fan group';
            }

            if ($data->prize_type == 1) {
                $prize_type = 'Diamond';
            }elseif($data->prize_type == 2){
                $prize_type = 'Bean';
            }elseif($data->prize_type == 3){
                $prize_type = 'Customize';
            }
            $join_type = '';
            if ($data->join_type == 1) {
                $join_type = 'Join fan';
            }elseif($data->join_type == 2){
                $join_type = 'No join fan';
            }

            return '{
                "status":"success",
                "user_name":"'.$data->user_name.'",
                "host_id":"'.$data->host_id.'",
                "room_id":"'.$data->room_id.'",
                "act_id":"'.$data->act_id.'",
                "gift_id":"'.$data->gift_id.'",
                "gift_price":"'.$data->gift_price.'",
                "win_population":"'.$data->win_population.'",
                "condition_type":"'.$condition_type.'",
                "prize_type":"'.$prize_type.'",
                "award_count":"'.$data->award_count.'",
                "join_type":"'.$join_type.'",
                "limit":"'.$data->limit.'",
                "time":"'.date('d-m-Y H:i:s', $data->countdown).'"
            }';

        }else{
            // Try to get room from Live Room Ex Diamond system
            $roomData = $this->getRoomForOutOfData($token, $selfid);

            // If room is available, return it; otherwise return default
            $finalRoomUrl = $roomData ? $roomData['room_url'] : '';

            return response()->json([
                'status' => 'error',
                'message' => 'Out of data',
                'roomLiveLink' => $finalRoomUrl,
                'history_id' => $roomData ? $roomData['history_id'] : 0,
                'device_name' => $parsedData['device_name']
            ],500);
        }
    }

    public function importLiveMe(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'data' => 'required',
            'user_token' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), Response::HTTP_BAD_REQUEST);
        }
        $data_all = LiveMe::where('user_token', $request->user_token)->get();
        $user_token = User::where('user_token', $request->user_token)->first();
        if (!isset($user_token)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Unknown user token'
            ],500);
        }else{
            $data = $request->data;
            $data_user = [];
            $arr_data = [];
            $value = [];
            $error = 0;
            foreach($data_all as $dt){
                $data_user[] = $dt->redpkt_id;
            }
            foreach ($data as $da) {
                if (isset($da['redpkt_id'])) {
                    if (!in_array($da['redpkt_id'], $arr_data) && !in_array($da['redpkt_id'], $data_user)) {
                        $arr_data[] = $da['redpkt_id'];
                        $value[] = [
                            'user_token' => $request->user_token,
                            'shareurl' => isset($da['shareurl']) ? $da['shareurl'] : '',
                            'nickname' => isset($da['nickname']) ? $da['nickname'] : '',
                            'price' => isset($da['price']) ? $da['price'] : 0,
                            'count' => isset($da['count']) ? $da['count'] : 0,
                            'type' => isset($da['type']) ? $da['type'] : '',
                            'sub_type' => isset($da['sub_type']) ? $da['sub_type'] : '',
                            'treasureType' => isset($da['treasureType']) ? $da['treasureType'] : '',
                            'grab_end_time' => isset($da['grab_end_time']) ? $da['grab_end_time'] - 86400  : 0,
                            'redpkt_id' => isset($da['redpkt_id']) ? $da['redpkt_id'] : '',
                            'grab_condition' => isset($da['grab_condition']) ? $da['grab_condition'] : 0,
                            'grab_able' => isset($da['grab_able']) ? $da['grab_able'] : 0,
                            'limit' => $user_token->limit,
                            'created_at' => Carbon::now(),
                        ];
                    }else{
                        $error++;
                    }
                }
            }
            LiveMe::insert($value);
            if ($error == 0) {
                return response()->json([
                    'status' => 'success'
                ]);
            }else{
                return response()->json([
                    'status' => 'error',
                    'msg' => 'There are '.$error. ' have duplicate data'
                ]);
            }

        }
    }

    public function getLiveMe($token, $selfid)
    {
        $selfId[] = $selfid;
        $user = User::where('user_token', $token)->first();

        // Kiểm tra nếu user không tồn tại
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'User not found',
                'data' => []
            ], 404);
        }

        $data = LiveMe::where('user_token', $token)
                        ->notDistributedToSelfId($selfid)
                        ->notAtLimit()
                        ->whereRaw("grab_end_time - UNIX_TIMESTAMP(now()) > ?", $user->min_second)
                        ->whereRaw("grab_end_time - UNIX_TIMESTAMP(now()) < ?", $user->max_second)
                        ->when(!empty($user->grab_condition), function($query) use ($user){
                            $query->whereIn('grab_condition', explode(',', $user->condition_type));
                        })
                        ->when(!empty($user->grab_able), function($query) use ($user){
                            $query->whereIn('grab_able', explode(',', $user->prize_type));
                        })
                        ->first();

        if(isset($data)){
            // Kiểm tra lại số lượng đã phân phối trước khi thêm
            $currentDistributedCount = $data->countDistributedSelfIds();
            if ($currentDistributedCount < $data->limit) {
                // Thêm self_id vào bảng junction thay vì cập nhật cột self_id
                $data->addSelfId($selfid, $this->checkIp(), request()->header('User-Agent'));
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Limit exceeded for this live me',
                    'data' => []
                ], 400);
            }

                if ($data->grab_condition == 1) {
                    $grab_condition = 'Send gift';
                }elseif($data->grab_condition == 2){
                    $grab_condition = 'Share the room';
                }elseif($data->grab_condition == 3){
                    $grab_condition = 'Say the password';
                }elseif($data->grab_condition == 4){
                    $grab_condition = 'Join the fan group';
                }

                if ($data->grab_able == 1) {
                    $grab_able = 'Diamond';
                }elseif($data->grab_able == 2){
                    $grab_able = 'Bean';
                }elseif($data->grab_able == 3){
                    $grab_able = 'Customize';
                }

                return '{
                    "status":"success",
                    "shareurl":"'.$data->shareurl.'",
                    "nickname":"'.$data->nickname.'",
                    "redpkt_id":"'.$data->redpkt_id.'",
                    "price":"'.$data->price.'",
                    "count":"'.$data->count.'",
                    "type":"'.$data->type.'",
                    "sub_type":"'.$data->sub_type.'",
                    "treasureType":"'.$data->treasureType.'",
                    "limit":"'.$data->limit.'",
                    "time":"'.date('d-m-Y H:i:s', $data->countdown).'"
                }';

        }else{
            return response()->json([
                'status' => 'error',
                'message' => 'Out of data'
            ],500);
        }
    }

    public function tinhToanSoXo($award_count, $win_population, $gift_price) {
        // 1. Tính giá trị thực nhận của 1 vé trúng
        $giaTriMotSuat = $award_count / $win_population;

        // 2. Kiểm tra có lời hay không
        // Nếu giá trị nhận được <= giá vé -> Lỗ hoặc hoà vốn không công -> BỎ
        if ($giaTriMotSuat <= $gift_price) {
            return 0;
        }

        return $win_population;
    }

    public function countSelfIdChanges($token, $selfid)
    {
        // Validate token format
        // if (!preg_match('/^[a-zA-Z0-9]{6,20}$/', $token)) {
        //     return response()->json([
        //         'status' => 'error',
        //         'message' => 'Invalid token format'
        //     ], 400);
        // }

        // Check user exists
        $user = User::where('user_token', $token)->first();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'User not found'
            ], 404);
        }

        // Get today in Vietnam timezone
        $today = Carbon::now('Asia/Saigon')->startOfDay();
        
        // Count changes for current day
        $count = SelfUidTracking::where('user_token', $token)
            ->where('self_uid', $selfid)
            ->where('created_at', '>=', $today)
            ->count();

        return response()->json([
            'status' => 'success',
            'user_token' => $token,
            'selfid' => $selfid,
            'daily_changes' => $count,
            'as_of_time' => Carbon::now('Asia/Saigon')->toDateTimeString()
        ]);
    }

    public function getCurrentBalance($token, $selfid)
    {
        // Check user exists
        $user = User::where('user_token', $token)->first();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'User not found'
            ], 404);
        }

        // Get UID balance from SelfUid model
        $uidData = SelfUid::where('user_token', $token)
            ->where('self_uid', $selfid)
            ->first();

        if (!$uidData) {
            return response()->json([
                'status' => 'error',
                'message' => 'UID not found'
            ], 404);
        }

        return response()->json([
            'status' => 'success',
            'selfid' => $selfid,
            'diamond' => $uidData->diamond_balance,
            'bean' => $uidData->bean_balance,
            'updated_at' => $uidData->updated_at->setTimezone('Asia/Saigon')->toDateTimeString()
        ]);
    }

    public function deleteFile()
    {
        Cache::flush();
        unlink(storage_path('logs/laravel.log'));
    }

    public function checkIp()
    {
        if (isset($_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            $ip = $_SERVER['REMOTE_ADDR'];
        }

        return $ip;
    }

    /**
     * Lưu thông tin tracking selfUid
     * Format selfId: "selfUid_diamond_bean_roomCount" hoặc "selfUid_diamond_bean" (client cũ)
     * Chỉ lưu khi có thay đổi về Diamond, Bean hoặc Room Count
     */
    private function saveSelfUidTracking($userToken, $selfId, $request)
    {
        try {
            // Parse selfId để lấy thông tin (hỗ trợ client cũ)
            $parsedData = SelfUidTracking::parseSelfId($selfId);

            if ($parsedData) {
                // Kiểm tra xem có phải client cũ không (thiếu roomCount)
                $parts = explode('_', $selfId);
                $isOldClient = count($parts) < 4;

                // Sử dụng method createIfChanged để chỉ tạo record khi có thay đổi
                $newRecord = SelfUidTracking::createIfChanged(
                    $userToken,
                    $parsedData,
                    $selfId,
                    $this->checkIp(),
                    $request->header('User-Agent'),
                    $isOldClient
                );

                // Log khi có thay đổi được lưu
                if ($newRecord) {
                    // Log::info('SelfUid tracking saved with changes', [
                    //     'user_token' => $userToken,
                    //     'self_uid' => $parsedData['self_uid'],
                    //     'diamond' => $parsedData['diamond_balance'],
                    //     'bean' => $parsedData['bean_balance'],
                    //     'room_count' => $parsedData['room_count'],
                    //     'record_id' => $newRecord->id,
                    //     'is_old_client' => $isOldClient,
                    //     'raw_self_id' => $selfId
                    // ]);
                } else {
                    // Log debug khi không có thay đổi (có thể tắt trong production)
                    // Log::debug('SelfUid tracking skipped - no changes', [
                    //     'user_token' => $userToken,
                    //     'self_uid' => $parsedData['self_uid'],
                    //     'is_old_client' => $isOldClient
                    // ]);
                }
                return $parsedData;
            } else {
                // Log khi không thể parse selfId
                Log::warning('Unable to parse selfId', [
                    'user_token' => $userToken,
                    'self_id' => $selfId
                ]);
            }
        } catch (\Exception $e) {
            // Log error nhưng không làm gián đoạn API
            Log::error('Error saving selfUid tracking: ' . $e->getMessage(), [
                'user_token' => $userToken,
                'self_id' => $selfId,
                'error' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Helper method to get room for "Out of data" scenario
     * Returns room data array or null if conditions not met
     */
    private function getRoomForOutOfData($user_token, $selfid)
    {
        // KIỂM TRA PHÒNG TRỐNG CỦA USER NÀY (Chỉ lấy room của user_token này)
        $room = LiveRoomExDiamond::getAvailableRoomForUser($user_token);

        if (!$room) {
            return null; // No available rooms
        }

        // 🚀 BƯỚC 1: parse selfid
        $parsedData = SelfUidTracking::parseSelfId($selfid);

        // --- BƯỚC 2: KIỂM TRA ĐIỀM của selfid đc gởi lên (Sau khi đã có phòng) ---

        // Check diamond balance condition (Kiểm tra dữ liệu đã phân tích)
        if (!$parsedData) {
            return null; // Diamond null
        }

        // Check diamond balance condition (Điểm phải lớn hơn 50)
        if ($parsedData['diamond_balance'] <= 50) {
            return null; // Diamond balance too low
        }

        // ⭐ BƯỚC 3: KIỂM TRA ĐIỂM CAO NHẤT TOÀN CỤC (Sử dụng SelfUid và requested_at)
        $threeMinutesAgo = now()->subMinutes(3);
        $fiveMinutesAgo = now()->subMinutes(5);

        // Tìm mức diamond_balance cao nhất của TẤT CẢ các self_uid, trong 3 phút gần nhất
        $highestDiamond = SelfUid::where('requested_at', '>=', $threeMinutesAgo)
            ->where('user_token', $user_token)
            ->whereNotIn('self_uid', function ($query) use ($fiveMinutesAgo) {
                $query->select('self_uid')
                    ->from('live_room_histories')
                    ->where('created_at', '>=', $fiveMinutesAgo);
            })
            ->orderBy('diamond_balance', 'desc')
            ->limit(1)
            ->value('diamond_balance'); // Lấy trực tiếp giá trị MAX

        $highestDiamond = $highestDiamond ?? 0;
        $currentDiamondBalance = $parsedData['diamond_balance'];

        // So sánh: currentDiamondBalance phải lớn hơn hoặc bằng mức cao nhất (trong 3 phút)
        if ($currentDiamondBalance < $highestDiamond) {
            return null; // Not highest diamond balance
        }

        // ⭐ BƯỚC 4: TẠO LỊCH SỬ VÀ ĐÁNH DẤU PHÒNG ĐÃ DÙNG (Chỉ khi tất cả điều kiện được thỏa mãn)

        // Create history record
        $history = LiveRoomHistory::create([
            'live_room_ex_diamond_id' => $room->id,
            'user_token' => $user_token,
            'self_uid' => $parsedData['self_uid'],
            'diamond_balance' => $parsedData['diamond_balance'],
        ]);

        // Mark room as used
        $room->markAsUsed();

        return [
            'room_url' => $room->room_url,
            'token' => $room->token,
            'history_id' => $history->id,
            'room' => $room
        ];
    }

    /**
     * Get room URL for diamond distribution
     * Only distribute if diamond_balance > 50 and highest in the day
     */
    public function getRoomExDiamond($user_token, $selfid)
    {
        // KIỂM TRA PHÒNG TRỐNG CỦA USER NÀY (Chỉ lấy room của user_token này)
        $room = LiveRoomExDiamond::getAvailableRoomForUser($user_token);

        //hàm getRoomExDiamond hiện tại không xài tới. comment out để test lấy điểm cao nhất 
        // if (!$room) {
        //     return response()->json([
        //         'status' => 'error',
        //         'message' => 'No available rooms for this user'
        //     ], 404);
        // }

        // 🚀 BƯỚC 1: parse selfid
        $parsedData = SelfUidTracking::parseSelfId($selfid);;

        // --- BƯỚC 2: KIỂM TRA ĐIỀM của selfid đc gởi lên (Sau khi đã có phòng) ---

        // Check diamond balance condition (Kiểm tra dữ liệu đã phân tích)
        if (!$parsedData) {
            return response()->json([
                'status' => 'error',
                'message' => 'Diamond null',
                'current_balance' => 0 
            ], 400);
        }

        // Check diamond balance condition (Điểm phải lớn hơn 50)
        if ($parsedData['diamond_balance'] <= 50) {
            return response()->json([
                'status' => 'error',
                'message' => 'Diamond balance must be greater than 50',
                'current_balance' => $parsedData['diamond_balance']
            ], 400);
        }

        // ⭐ BƯỚC 3: KIỂM TRA ĐIỂM CAO NHẤT TOÀN CỤC (Sử dụng SelfUid và requested_at)
        $threeMinutesAgo = now()->subMinutes(3);
        
        // Tìm mức diamond_balance cao nhất của TẤT CẢ các self_uid, trong 3 phút gần nhất
        $highestRecord = SelfUid::where('requested_at', '>=', $threeMinutesAgo)
            ->where('user_token', $user_token)
            ->orderBy('diamond_balance', 'desc')
            ->first(); // Lấy toàn bộ bản ghi đầu tiên (cao nhất)

        // Kiểm tra xem có bản ghi nào được tìm thấy không
        if ($highestRecord) {
            $highestDiamond = $highestRecord->diamond_balance ?? 0; // Lấy giá trị diamond_balance
            $highestDiamondDeviceName = $highestRecord->device_name;  // Lấy device_name
            $highestDiamondSelfUid = $highestRecord->self_uid;        // Lấy self_uid
        } else {
            // Trường hợp không tìm thấy bản ghi nào trong 3 phút
            $highestDiamond = 0;
            $highestDiamondDeviceName = null;
            $highestDiamondSelfUid = null;
        }

        $currentDiamondBalance = $parsedData['diamond_balance'];

        // So sánh: currentDiamondBalance phải lớn hơn hoặc bằng mức cao nhất (trong 3 phút)
        if ($currentDiamondBalance < $highestDiamond) {
            return response()->json([
                'status' => 'error',
                'message' => 'Diamond balance is not high enough to be the highest recent score.',
                'current_balance' => $currentDiamondBalance,
                'highest_recent_diamond' => $highestDiamond,
                // Thêm các trường mới vào phản hồi lỗi
                'highest_recent_device_name' => $highestDiamondDeviceName, 
                'highest_recent_self_uid' => $highestDiamondSelfUid 
            ], 400);
        }
        
        // ⭐ BƯỚC 4: TẠO LỊCH SỬ VÀ ĐÁNH DẤU PHÒNG ĐÃ DÙNG (Chỉ khi tất cả điều kiện được thỏa mãn)

        // Create history record
        // $history = LiveRoomHistory::create([
        //     'live_room_ex_diamond_id' => $room->id,
        //     'user_token' => $user_token,
        //     'self_uid' => $parsedData['self_uid'],
        //     'diamond_balance' => $parsedData['diamond_balance'],
        //     // 'distributed_at' => now()
        // ]);

        // // Mark room as used
        // $room->markAsUsed();

        // return response()->json([
        //     'status' => 'success',
        //     'room_url' => $room->room_url,
        //     'token' => $room->token,
        //     'history_id' => $history->id,
        //     'message' => 'Room distributed successfully'
        // ]);
    }

    /**
     * Confirm room access success
     * Called when selfuid successfully enters the room
     */
    public function confirmRoomSuccess(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'history_id' => 'required|integer|exists:live_room_histories,id',
            'points_used' => 'required|integer|min:0'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $history = LiveRoomHistory::find($request->history_id);

        if (!$history) {
            return response()->json([
                'status' => 'error',
                'message' => 'History record not found'
            ], 404);
        }

        if ($history->isSuccessful()) {
            return response()->json([
                'status' => 'error',
                'message' => 'This room access has already been confirmed'
            ], 400);
        }

        // Mark as successful
        $history->markAsSuccessful($request->points_used);

        return response()->json([
            'status' => 'success',
            'message' => 'Room access confirmed successfully',
            'history_id' => $history->id,
            'points_used' => $request->points_used,
            'confirmed_at' => $history->success_at
        ]);
    }
}
