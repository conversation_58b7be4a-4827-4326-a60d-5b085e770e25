<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DataUser extends Model
{
    use HasFactory;

    protected $table = 'data_users';

    protected $fillable = [
        'user_token',
        'user_name',
        'host_id',
        'room_id',
        'condition_type',
        'prize_type',
        'award_count',
        'win_population',
        'act_id',
        'gift_id',
        'gift_price',
        'countdown',
        'limit',
        'join_type',
        'self_id'
    ];

    protected $casts = [
        'self_id' => 'array'
    ];

    public function user()
    {
        return $this->belongsTo(User::class,'user_token', 'user_token');
    }
}
