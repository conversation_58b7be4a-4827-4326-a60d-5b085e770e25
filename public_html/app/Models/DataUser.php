<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DataUser extends Model
{
    use HasFactory;

    protected $table = 'data_users';

    protected $fillable = [
        'user_token',
        'user_name',
        'host_id',
        'room_id',
        'condition_type',
        'prize_type',
        'award_count',
        'win_population',
        'act_id',
        'gift_id',
        'gift_price',
        'countdown',
        'limit',
        'join_type',
        'self_id'
    ];

    protected $casts = [
        'self_id' => 'array'
    ];

    public function user()
    {
        return $this->belongsTo(User::class,'user_token', 'user_token');
    }

    /**
     * Relationship với DataUserSelfId (many-to-many through junction table)
     */
    public function distributedSelfIds()
    {
        return $this->hasMany(DataUserSelfId::class, 'data_user_id', 'id');
    }

    /**
     * Kiểm tra xem self_id đã được phân phối chưa
     */
    public function hasSelfId($selfId)
    {
        return DataUserSelfId::isDistributed($this->id, $selfId);
    }

    /**
     * Thêm self_id vào danh sách đã phân phối
     */
    public function addSelfId($selfId, $ipAddress = null, $userAgent = null)
    {
        if (!$this->hasSelfId($selfId)) {
            return DataUserSelfId::createDistribution($this->id, $selfId, $ipAddress, $userAgent);
        }
        return null;
    }

    /**
     * Lấy danh sách self_id đã được phân phối
     */
    public function getDistributedSelfIds()
    {
        return DataUserSelfId::getDistributedSelfIds($this->id);
    }

    /**
     * Đếm số lượng self_id đã được phân phối
     */
    public function countDistributedSelfIds()
    {
        return DataUserSelfId::countDistributed($this->id);
    }

    /**
     * Scope để lọc những data_user chưa được phân phối cho self_id cụ thể
     */
    public function scopeNotDistributedToSelfId($query, $selfId)
    {
        return $query->whereNotExists(function ($subQuery) use ($selfId) {
            $subQuery->select('id')
                     ->from('data_user_self_ids')
                     ->whereColumn('data_user_self_ids.data_user_id', 'data_users.id')
                     ->where('data_user_self_ids.self_id', $selfId);
        });
    }
}
