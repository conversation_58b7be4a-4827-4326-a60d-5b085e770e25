<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LiveMe extends Model
{
    use HasFactory;

    protected $table = 'live_mes';

    protected $casts = [
        'self_id' => 'array'
    ];

    /**
     * Relationship với LiveMeSelfId (many-to-many through junction table)
     */
    public function distributedSelfIds()
    {
        return $this->hasMany(LiveMeSelfId::class, 'live_me_id', 'id');
    }

    /**
     * Kiểm tra xem self_id đã được phân phối chưa
     */
    public function hasSelfId($selfId)
    {
        return LiveMeSelfId::isDistributed($this->id, $selfId);
    }

    /**
     * Thêm self_id vào danh sách đã phân phối
     */
    public function addSelfId($selfId, $ipAddress = null, $userAgent = null)
    {
        if (!$this->hasSelfId($selfId)) {
            // Kiểm tra limit trước khi thêm
            $currentCount = $this->countDistributedSelfIds();
            if ($this->limit && $currentCount >= $this->limit) {
                return null; // Đã đạt limit
            }
            return LiveMeSelfId::createDistribution($this->id, $selfId, $ipAddress, $userAgent);
        }
        return null;
    }

    /**
     * Lấy danh sách self_id đã được phân phối
     */
    public function getDistributedSelfIds()
    {
        return LiveMeSelfId::getDistributedSelfIds($this->id);
    }

    /**
     * Đếm số lượng self_id đã được phân phối
     */
    public function countDistributedSelfIds()
    {
        return LiveMeSelfId::countDistributed($this->id);
    }

    /**
     * Scope để lọc những live_me chưa được phân phối cho self_id cụ thể
     */
    public function scopeNotDistributedToSelfId($query, $selfId)
    {
        return $query->whereNotExists(function ($subQuery) use ($selfId) {
            $subQuery->select('id')
                     ->from('live_me_self_ids')
                     ->whereColumn('live_me_self_ids.live_me_id', 'live_mes.id')
                     ->where('live_me_self_ids.self_id', $selfId);
        });
    }

    /**
     * Scope để lọc những live_me chưa đạt limit
     */
    public function scopeNotAtLimit($query)
    {
        return $query->where('limit', '>', 0)
                     ->whereRaw('(SELECT COUNT(*) FROM live_me_self_ids WHERE live_me_id = live_mes.id) < live_mes.limit');
    }
}
