<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class DataUserSelfId extends Model
{
    use HasFactory;

    protected $table = 'data_user_self_ids';

    protected $fillable = [
        'data_user_id',
        'self_id',
        'distributed_at',
        'ip_address',
        'user_agent'
    ];

    protected $casts = [
        'distributed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Relationship với DataUser
     */
    public function dataUser()
    {
        return $this->belongsTo(DataUser::class, 'data_user_id', 'id');
    }

    /**
     * Scope để tìm theo data_user_id
     */
    public function scopeByDataUser($query, $dataUserId)
    {
        return $query->where('data_user_id', $dataUserId);
    }

    /**
     * Scope để tìm theo self_id
     */
    public function scopeBySelfId($query, $selfId)
    {
        return $query->where('self_id', $selfId);
    }

    /**
     * Scope để tìm theo khoảng thời gian phân phối
     */
    public function scopeDistributedBetween($query, $startDate, $endDate)
    {
        return $query->whereBetween('distributed_at', [$startDate, $endDate]);
    }

    /**
     * Scope để tìm theo ngày phân phối
     */
    public function scopeDistributedToday($query)
    {
        return $query->whereDate('distributed_at', Carbon::today());
    }

    /**
     * Kiểm tra xem self_id đã được phân phối cho data_user chưa
     */
    public static function isDistributed($dataUserId, $selfId)
    {
        return self::where('data_user_id', $dataUserId)
                   ->where('self_id', $selfId)
                   ->exists();
    }

    /**
     * Lấy danh sách self_id đã được phân phối cho data_user
     */
    public static function getDistributedSelfIds($dataUserId)
    {
        return self::where('data_user_id', $dataUserId)
                   ->pluck('self_id')
                   ->toArray();
    }

    /**
     * Đếm số lượng self_id đã được phân phối cho data_user
     */
    public static function countDistributed($dataUserId)
    {
        return self::where('data_user_id', $dataUserId)->count();
    }

    /**
     * Tạo bản ghi phân phối mới với duplicate protection
     */
    public static function createDistribution($dataUserId, $selfId, $ipAddress = null, $userAgent = null)
    {
        try {
            return self::create([
                'data_user_id' => $dataUserId,
                'self_id' => $selfId,
                'distributed_at' => Carbon::now(),
                'ip_address' => $ipAddress,
                'user_agent' => $userAgent
            ]);
        } catch (\Illuminate\Database\QueryException $e) {
            // Nếu là duplicate entry error, trả về existing record
            if ($e->getCode() == 23000 && strpos($e->getMessage(), 'Duplicate entry') !== false) {
                return self::where('data_user_id', $dataUserId)
                          ->where('self_id', $selfId)
                          ->first();
            }
            // Nếu là lỗi khác, throw lại
            throw $e;
        }
    }
}
