<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class LiveMeSelfId extends Model
{
    use HasFactory;

    protected $table = 'live_me_self_ids';

    protected $fillable = [
        'live_me_id',
        'self_id',
        'distributed_at',
        'ip_address',
        'user_agent'
    ];

    protected $casts = [
        'distributed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Relationship với LiveMe
     */
    public function liveMe()
    {
        return $this->belongsTo(LiveMe::class, 'live_me_id', 'id');
    }

    /**
     * Scope để tìm theo live_me_id
     */
    public function scopeByLiveMe($query, $liveMeId)
    {
        return $query->where('live_me_id', $liveMeId);
    }

    /**
     * Scope để tìm theo self_id
     */
    public function scopeBySelfId($query, $selfId)
    {
        return $query->where('self_id', $selfId);
    }

    /**
     * Scope để tìm theo khoảng thời gian phân phối
     */
    public function scopeDistributedBetween($query, $startDate, $endDate)
    {
        return $query->whereBetween('distributed_at', [$startDate, $endDate]);
    }

    /**
     * Scope để tìm theo ngày phân phối
     */
    public function scopeDistributedToday($query)
    {
        return $query->whereDate('distributed_at', Carbon::today());
    }

    /**
     * Kiểm tra xem self_id đã được phân phối cho live_me chưa
     */
    public static function isDistributed($liveMeId, $selfId)
    {
        return self::where('live_me_id', $liveMeId)
                   ->where('self_id', $selfId)
                   ->exists();
    }

    /**
     * Lấy danh sách self_id đã được phân phối cho live_me
     */
    public static function getDistributedSelfIds($liveMeId)
    {
        return self::where('live_me_id', $liveMeId)
                   ->pluck('self_id')
                   ->toArray();
    }

    /**
     * Đếm số lượng self_id đã được phân phối cho live_me
     */
    public static function countDistributed($liveMeId)
    {
        return self::where('live_me_id', $liveMeId)->count();
    }

    /**
     * Tạo bản ghi phân phối mới
     */
    public static function createDistribution($liveMeId, $selfId, $ipAddress = null, $userAgent = null)
    {
        return self::create([
            'live_me_id' => $liveMeId,
            'self_id' => $selfId,
            'distributed_at' => Carbon::now(),
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent
        ]);
    }
}
