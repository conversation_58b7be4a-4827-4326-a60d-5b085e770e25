<div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title" id="exampleModalLabel">S<PERSON>a d<PERSON> liệu</h5>
        </div>
        <form action="{{ route('update.data', $data->id) }}" method="post" enctype="multipart/form-data" id="edit-data">
            @csrf
            <div class="modal-body">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label class="form-control-label" for="basic-url"><PERSON><PERSON>n hiển thị</label>
                            <input type="text" class="form-control" name="user_name" placeholder="Tên hiển thị" value="{{ $data->user_name }}">
                        </div>
                      </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label class="form-control-label" for="basic-url">Host ID</label>
                            <input type="number" class="form-control" name="host_id" placeholder="Host ID" value="{{ $data->host_id }}">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label class="form-control-label" for="basic-url">Room ID</label>
                            <input type="number" class="form-control" name="room_id" placeholder="Room ID" value="{{ $data->room_id }}">
                        </div>
                      </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label class="form-control-label" for="basic-url">Act ID</label>
                            <input type="text" class="form-control" name="act_id" placeholder="Act ID" value="{{ $data->act_id }}">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label class="form-control-label" for="basic-url">Gift ID</label>
                            <input type="text" class="form-control" name="gift_id" placeholder="Gift ID" value="{{ $data->gift_id }}">
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label class="form-control-label" for="basic-url">Giá gift</label>
                            <input type="number" step="0.01" class="form-control" name="gift_price" placeholder="Giá gift" value="{{ $data->gift_price }}">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label for="exampleSelectRounded0">Loại điều kiện</label>
                            <select class="custom-select rounded-0" name="condition_type" id="exampleSelectRounded0">
                              <option value="0">Chọn ngẫu nhiên</option>
                              <option value="1" @if ($data->condition_type == 1) selected @endif>Send gift</option>
                              <option value="2" @if ($data->condition_type == 2) selected @endif>Share the room</option>
                              <option value="3" @if ($data->condition_type == 3) selected @endif>Say the password</option>
                              <option value="4" @if ($data->condition_type == 4) selected @endif>Join the fan group</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label class="form-control-label" for="basic-url">Loại giải thưởng</label>
                            <select class="custom-select rounded-0" name="prize_type" id="exampleSelectRounded0">
                                <option value="0">Chọn ngẫu nhiên</option>
                                <option value="1" @if ($data->prize_type == 1) selected @endif>Diamond</option>
                                <option value="2" @if ($data->prize_type == 2) selected @endif>Bean</option>
                                <option value="3" @if ($data->prize_type == 3) selected @endif>Customize</option>
                            </select>
                        </div>
                      </div>
                </div>

                    <div class="col-lg-6">
                        <div class="form-group">
                            <label class="form-control-label" for="basic-url">Thời gian </label>
                            <div class="input-group date" id="reservationdatetime" data-target-input="nearest">
                                <input type="text" name="countdown" value="{{ date('d/m/Y H:i:s', $data->countdown) }}" class="form-control datetimepicker-input" data-target="#reservationdatetime" data-toggle="datetimepicker">
                                <div class="input-group-append">
                                    <div class="input-group-text"><i class="fa fa-calendar"></i></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn bg-gradient-info submit">Cập nhật</button>
            </div>
        </form>
    </div>
</div>
<script>
    $(function(){
        $('#reservationdatetime').datetimepicker({
            format:'DD/MM/YYYY HH:mm:ss',
            icons: { time: 'far fa-clock' }
        });
    })
    $('#edit-data').submit(function(e){
        e.preventDefault();
        $('.submit').attr('disabled', true);
        let data = new FormData($('#edit-data')[0]);
        let url = $(this).attr('action');
        $.ajax({
            url: url,
            type: "post",
            data: data,
            contentType: false,
            processData: false,
            success: function(response) {
                if (response.success == true) {
                    $('div.data_modal').modal('hide');
                    $('#data_table').DataTable().ajax.reload();
                    toastr.options = {
                        "progressBar": true,
                        "timeOut": "2000",
                    }
                    toastr.success('Sửa thành công');
                }
            }
        });
    });
</script>
