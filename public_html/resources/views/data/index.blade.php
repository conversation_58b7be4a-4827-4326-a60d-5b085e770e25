@extends('layouts.master')
@section('title')
    <title>Dữ liệu</title>
@endsection
@section('style')
<style>
.select2-container .select2-selection--single{
    height: 38px;
}
.select2-container--default .select2-results__option[aria-disabled=true] {
    color: #000;
}
</style>
@endsection
@section('content')
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
      <div class="row mb-2">
        <div class="col-sm-8">
          <h1 class="m-0">Dữ liệu @if(Auth::user()->role == 1 && $user->role != 1)- Thành viên: {{ $user->name }} - {{ $user->email }} @endif</h1>
        </div><!-- /.col -->
        <div class="col-sm-4">
          <ol class="breadcrumb float-sm-right">
            <li class="breadcrumb-item"><a href="#">Home</a></li>
            <li class="breadcrumb-item active">Dữ liệu</li>
          </ol>
        </div><!-- /.col -->
      </div><!-- /.row -->
    </div><!-- /.container-fluid -->
</div>
  <!-- /.content-header -->

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                <div class="card-header">
                    <div class="row">
                        <div class="col-lg-4">
                            <div class="form-inline">
                                <div class="input-group" data-widget="sidebar-search">
                                <input class="form-control" type="text" placeholder="Tìm kiếm..." id="search-btn" class="aria-label">
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-8 group-btn text-right">
                            <button type="button" class="btn btn-primary add_data" data-container=".data_modal"
                            data-href="{{ route('data.create', $token) }}"><i class="fa fa-plus"></i> Thêm dữ liệu</button>
                        </div>
                    </div>
                </div>
                <!-- /.card-header -->
                <div class="card-body">
                    <table id="data_table" class="table table-bordered table-hover">
                    <thead>
                        <tr>
                            <th>Tên hiển thị</th>
                            <th>Host ID</th>
                            <th>Room ID</th>
                            <th>ACT ID</th>
                            <th>Gift ID</th>
                            <th>Giá gift</th>
                            <th>Win population</th>
                            <th>Loại điều kiện</th>
                            <th>Loại giải thưởng</th>
                            <th>Số giải thưởng</th>
                            <th>Loại tham gia</th>
                            <th>Thời gian</th>
                            <!-- <th>Thao tác</th> -->
                        </tr>
                    </thead>
                    </table>
                </div>
                <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <div class="modal fade data_modal" id="data_modal" tabindex="-1" role="dialog">
        </div>
    </div><!-- /.container-fluid -->
</section>
<!-- /.content -->
@endsection
@section('script')
<script>
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    var debounceTripDetail = null;
    $('#search-btn').on('input', function(){
        clearTimeout(debounceTripDetail);
        debounceTripDetail = setTimeout(() => {
            data_table.search($(this).val()).draw();
        }, 500);
    });

    var data_table = $('#data_table').DataTable({
        "destroy": true,
        "lengthChange": false,
        "searching": true,
        "ordering": false,
        "info": true,
        "autoWidth": false,
        "responsive": true,
        "pageLength": 15,
        // aaSorting: [
        //     [5, 'desc']
        // ],
        "pagingType": "full_numbers",
        "language": {
            "info": 'Hiển thị _START_ đến _END_ của _TOTAL_ mục',
            "infoEmpty": 'Hiển thị 0 đến 0 của 0 mục',
            "infoFiltered": '',
            "infoPostFix": "",
            "thousands": ",",
            "lengthMenu": 'Hiển thị _MENU_ mục',
            "loadingRecords": 'Đang tải...',
            "processing": 'Đang xử lý...',
            "emptyTable": 'Không có dữ liệu',
            "zeroRecords": 'Không tìm thấy kết quả',
            "search": 'Tìm kiếm',
            "paginate": {
                'first': '<i class="fa fa-angle-double-left"></i>',
                'previous': '<i class="fa fa-angle-left" ></i>',
                'next': '<i class="fa fa-angle-right" ></i>',
                'last': '<i class="fa fa-angle-double-right"></i>'
            },
        },
        ajax: {
            url: "{{ route('data', $token) }}",
        },
        order: [],
        "columns":[
            {"data": "user_name" },
            {"data": "host_id", class: 'text-center' },
            {"data": "room_id", class: 'text-center' },
            {"data": "act_id", class: 'text-center' },
            {"data": "gift_id", class: 'text-center' },
            {"data": "gift_price", class: 'text-center' },
            {"data": "win_population", class: 'text-center' },
            {"data": "condition_type" },
            {"data": "prize_type" },
            {"data": "award_count", class: 'text-center' },
            {"data": "join_type", class: 'text-center' },
            {"data": "countdown", class: 'text-center' },
            // {"data": "action", orderable: false}
        ],
    });

    $(document).on('click', '.add_data', function(e) {
        e.preventDefault();
        $('div.data_modal').load($(this).attr('data-href'), function() {
            $(this).modal('show');
        });
    });

    $(document).on('click', '.edit_data', function(e) {
        e.preventDefault();
        $('div.data_modal').load($(this).attr('data-href'), function() {
            $(this).modal('show');
        });
    });

     // delete
     $(document).on('click', '.delete_data', function(e) {
        let name = $(this).data('name');
        var url = $(this).data('href');
        Swal.fire({
            title: 'Bạn muốn xóa dữ liệu ' +name,
            icon: 'warning',
            showCancelButton: true,
            cancelButtonColor: '#d33',
            confirmButtonColor: '#3085d6',
            cancelButtonText: "Hủy",
            confirmButtonText: "Xóa",
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    method: "GET",
                    url: url,
                    dataType: "json",
                    success: function(result) {
                        if (result.success == true) {
                            toastr.options = {
                                "progressBar": true,
                                "timeOut": "2000",
                                }
                            toastr.success('Xóa thành công!');
                        }
                        data_table.ajax.reload();
                    }
                })
            }
        });
    });

    // delete all
    $(document).on('click', '.delete_all', function(e) {
        Swal.fire({
            title: 'Bạn muốn xóa tất cả dữ liệu?',
            icon: 'warning',
            showCancelButton: true,
            cancelButtonColor: '#d33',
            confirmButtonColor: '#3085d6',
            cancelButtonText: "Hủy",
            confirmButtonText: "Xóa",
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    method: "GET",
                    url: "{{ route('delete.all') }}",
                    data: {
                        user_token: "{{ $token }}"
                    },
                    dataType: "json",
                    success: function(result) {
                        if (result.success == true) {
                            toastr.success('Xóa thành công!');
                            data_used_table.ajax.reload(null, false);
                        }else{
                            toastr.error('Không tồn tại dữ liệu!');
                        }

                    }
                })
            }
        });
    });
</script>
@endsection
