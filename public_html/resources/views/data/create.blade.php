<div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title" id="exampleModalLabel">Thê<PERSON> dữ liệu</h5>
        </div>
        <form action="#" method="post" enctype="multipart/form-data" id="import-data">
            @csrf
            <div class="modal-body">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label class="form-control-label" for="basic-url">Tên hiển thị</label>
                            <input type="text" class="form-control" name="user_name" placeholder="Tên hiển thị">
                        </div>
                      </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label class="form-control-label" for="basic-url">Host ID</label>
                            <input type="number" class="form-control" name="host_id" placeholder="Host ID">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label class="form-control-label" for="basic-url">Room ID</label>
                            <input type="number" class="form-control" name="room_id" placeholder="Room ID">
                        </div>
                      </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label class="form-control-label" for="basic-url">Act ID</label>
                            <input type="text" class="form-control" name="act_id" placeholder="Act ID">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label class="form-control-label" for="basic-url">Gift ID</label>
                            <input type="text" class="form-control" name="gift_id" placeholder="Gift ID">
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label class="form-control-label" for="basic-url">Giá gift</label>
                            <input type="number" step="0.01" class="form-control" name="gift_price" placeholder="Giá gift">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label class="form-control-label" for="basic-url">Loại điều kiện </label>
                            <select class="custom-select rounded-0" name="condition_type" id="exampleSelectRounded0">
                                <option value="0">Chọn ngẫu nhiên</option>
                                <option value="1">Send gift</option>
                                <option value="2">Share the room</option>
                                <option value="3">Say the password</option>
                                <option value="4">Join the fan group</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label class="form-control-label" for="basic-url">Loại giải thưởng</label>
                            <select class="custom-select rounded-0" name="prize_type" id="exampleSelectRounded0">
                                <option value="0">Chọn ngẫu nhiên</option>
                                <option value="1">Diamond</option>
                                <option value="2">Bean</option>
                                <option value="3">Customize</option>
                            </select>
                        </div>
                      </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label class="form-control-label" for="basic-url">Thời gian </label>
                            <div class="input-group date" id="reservationdatetime" data-target-input="nearest">
                                <input type="text" name="countdown" class="form-control datetimepicker-input" data-target="#reservationdatetime" data-toggle="datetimepicker">
                                <div class="input-group-append">
                                    <div class="input-group-text"><i class="fa fa-calendar"></i></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn bg-gradient-info submit">Thêm</button>
            </div>
        </form>
    </div>
</div>
<script>
    $(function(){
        $('#reservationdatetime').datetimepicker({
            format:'DD/MM/YYYY HH:mm:ss',
            icons: { time: 'far fa-clock' }
        });
    })

    $('#import-data').submit(function(e){
        e.preventDefault();
        $('.submit').attr('disabled', true);
        let data = new FormData($('#import-data')[0]);
        $.ajax({
            url: "{{ route('import.data', $token) }}",
            type: "post",
            data: data,
            contentType: false,
            processData: false,
            success: function(response) {
                if (response.success == true) {
                    $('div.data_modal').modal('hide');
                    $('#data_table').DataTable().ajax.reload();
                    toastr.options = {
                        "progressBar": true,
                        "timeOut": "2000",
                        }
                        toastr.success('Thêm thành công');
                }
            },
            error: function(response) {
                var error = JSON.parse(response.responseText);
                toastr.error(error.msg);
                $('.submit').removeAttr('disabled');
            }
        });
    });
</script>
